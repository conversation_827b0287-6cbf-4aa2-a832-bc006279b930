import { useState } from 'react';
import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function PlacementExample() {
  const [anchorElTop, setAnchorElTop] = useState<HTMLElement | null>(null);
  const [anchorElBottom, setAnchorElBottom] = useState<HTMLElement | null>(null);
  const [anchorElLeft, setAnchorElLeft] = useState<HTMLElement | null>(null);
  const [anchorElRight, setAnchorElRight] = useState<HTMLElement | null>(null);

  const handleClickTop = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElTop(event.currentTarget);
  };

  const handleClickBottom = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElBottom(event.currentTarget);
  };

  const handleClickLeft = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElLeft(event.currentTarget);
  };

  const handleClickRight = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElRight(event.currentTarget);
  };

  const handleCloseTop = () => {
    setAnchorElTop(null);
  };

  const handleCloseBottom = () => {
    setAnchorElBottom(null);
  };

  const handleCloseLeft = () => {
    setAnchorElLeft(null);
  };

  const handleCloseRight = () => {
    setAnchorElRight(null);
  };

  return (
    <Box sx={{ display: 'flex', gap: '24px', flexWrap: 'wrap', justifyContent: 'center', p: 4 }}>
      <Button variant="outlined" onClick={handleClickTop}>
        Top
      </Button>
      <Popover
        open={Boolean(anchorElTop)}
        anchorEl={anchorElTop}
        onClose={handleCloseTop}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="titleSmall" sx={{ mb: 1 }}>
            Top Placement
          </Typography>
          <Typography variant="bodySmall">This popover should appear ABOVE the button</Typography>
        </Box>
      </Popover>

      <Button variant="outlined" onClick={handleClickBottom}>
        Bottom
      </Button>
      <Popover
        open={Boolean(anchorElBottom)}
        anchorEl={anchorElBottom}
        onClose={handleCloseBottom}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="titleSmall" sx={{ mb: 1 }}>
            Bottom Placement
          </Typography>
          <Typography variant="bodySmall">This popover is placed on the bottom</Typography>
        </Box>
      </Popover>

      <Button variant="outlined" onClick={handleClickLeft}>
        Left
      </Button>
      <Popover
        open={Boolean(anchorElLeft)}
        anchorEl={anchorElLeft}
        onClose={handleCloseLeft}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'center',
          horizontal: 'right',
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="titleSmall" sx={{ mb: 1 }}>
            Left Placement
          </Typography>
          <Typography variant="bodySmall">This popover should appear to the LEFT of the button</Typography>
        </Box>
      </Popover>

      <Button variant="outlined" onClick={handleClickRight}>
        Right
      </Button>
      <Popover
        open={Boolean(anchorElRight)}
        anchorEl={anchorElRight}
        onClose={handleCloseRight}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'center',
          horizontal: 'left',
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="titleSmall" sx={{ mb: 1 }}>
            Right Placement
          </Typography>
          <Typography variant="bodySmall">This popover is placed on the right</Typography>
        </Box>
      </Popover>
    </Box>
  );
}
