import { useState } from 'react';
import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function PlacementTestExample() {
  const [anchorElTop, setAnchorElTop] = useState<HTMLElement | null>(null);
  const [anchorElBottom, setAnchorElBottom] = useState<HTMLElement | null>(null);
  const [anchorElLeft, setAnchorElLeft] = useState<HTMLElement | null>(null);
  const [anchorElRight, setAnchorElRight] = useState<HTMLElement | null>(null);

  const handleClickTop = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElTop(event.currentTarget);
  };

  const handleClickBottom = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElBottom(event.currentTarget);
  };

  const handleClickLeft = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElLeft(event.currentTarget);
  };

  const handleClickRight = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElRight(event.currentTarget);
  };

  const handleCloseTop = () => {
    setAnchorElTop(null);
  };

  const handleCloseBottom = () => {
    setAnchorElBottom(null);
  };

  const handleCloseLeft = () => {
    setAnchorElLeft(null);
  };

  const handleCloseRight = () => {
    setAnchorElRight(null);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '48px', alignItems: 'center', p: 8 }}>
      <Typography variant="titleLarge">Popover Placement Test</Typography>
      
      {/* Top Placement Test */}
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <Typography variant="titleMedium">Top Placement (should appear ABOVE button)</Typography>
        <Button variant="outlined" onClick={handleClickTop}>
          Click for Top Popover
        </Button>
        <Popover
          open={Boolean(anchorElTop)}
          anchorEl={anchorElTop}
          onClose={handleCloseTop}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
        >
          <Box sx={{ p: 2, backgroundColor: 'lightblue' }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              TOP POPOVER
            </Typography>
            <Typography variant="bodySmall">Should be ABOVE the button</Typography>
          </Box>
        </Popover>
      </Box>

      {/* Bottom Placement Test */}
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <Typography variant="titleMedium">Bottom Placement (should appear BELOW button)</Typography>
        <Button variant="outlined" onClick={handleClickBottom}>
          Click for Bottom Popover
        </Button>
        <Popover
          open={Boolean(anchorElBottom)}
          anchorEl={anchorElBottom}
          onClose={handleCloseBottom}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          <Box sx={{ p: 2, backgroundColor: 'lightgreen' }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              BOTTOM POPOVER
            </Typography>
            <Typography variant="bodySmall">Should be BELOW the button</Typography>
          </Box>
        </Popover>
      </Box>

      {/* Left Placement Test */}
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <Typography variant="titleMedium">Left Placement (should appear to the LEFT of button)</Typography>
        <Button variant="outlined" onClick={handleClickLeft}>
          Click for Left Popover
        </Button>
        <Popover
          open={Boolean(anchorElLeft)}
          anchorEl={anchorElLeft}
          onClose={handleCloseLeft}
          anchorOrigin={{
            vertical: 'center',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'center',
            horizontal: 'right',
          }}
        >
          <Box sx={{ p: 2, backgroundColor: 'lightyellow' }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              LEFT POPOVER
            </Typography>
            <Typography variant="bodySmall">Should be to the LEFT of the button</Typography>
          </Box>
        </Popover>
      </Box>

      {/* Right Placement Test */}
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <Typography variant="titleMedium">Right Placement (should appear to the RIGHT of button)</Typography>
        <Button variant="outlined" onClick={handleClickRight}>
          Click for Right Popover
        </Button>
        <Popover
          open={Boolean(anchorElRight)}
          anchorEl={anchorElRight}
          onClose={handleCloseRight}
          anchorOrigin={{
            vertical: 'center',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'center',
            horizontal: 'left',
          }}
        >
          <Box sx={{ p: 2, backgroundColor: 'lightcoral' }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              RIGHT POPOVER
            </Typography>
            <Typography variant="bodySmall">Should be to the RIGHT of the button</Typography>
          </Box>
        </Popover>
      </Box>
    </Box>
  );
}
