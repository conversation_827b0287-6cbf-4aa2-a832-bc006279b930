import { useState } from 'react';
import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function ArrowExample() {
  const [anchorEl1, setAnchorEl1] = useState<HTMLElement | null>(null);
  const [anchorEl2, setAnchorEl2] = useState<HTMLElement | null>(null);
  const [anchorEl3, setAnchorEl3] = useState<HTMLElement | null>(null);
  const [anchorEl4, setAnchorEl4] = useState<HTMLElement | null>(null);

  const handleClick1 = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl1(event.currentTarget);
  };

  const handleClick2 = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl2(event.currentTarget);
  };

  const handleClick3 = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl3(event.currentTarget);
  };

  const handleClick4 = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl4(event.currentTarget);
  };

  const handleClose1 = () => {
    setAnchorEl1(null);
  };

  const handleClose2 = () => {
    setAnchorEl2(null);
  };

  const handleClose3 = () => {
    setAnchorEl3(null);
  };

  const handleClose4 = () => {
    setAnchorEl4(null);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '48px', alignItems: 'center', p: 4 }}>
      <Typography variant="titleLarge">Popover with Arrow Examples (FIXED - Clean Triangles)</Typography>

      <Box sx={{ display: 'flex', gap: '24px', alignItems: 'center', flexWrap: 'wrap' }}>
        {/* Bottom Arrow */}
        <Button variant="outlined" onClick={handleClick1}>
          Bottom Arrow
        </Button>
        <Popover
          open={Boolean(anchorEl1)}
          anchorEl={anchorEl1}
          onClose={handleClose1}
          showArrow={true}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          <Box sx={{ p: 2, maxWidth: 200 }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              Bottom Arrow
            </Typography>
            <Typography variant="bodySmall">This popover has an arrow pointing up to the trigger</Typography>
          </Box>
        </Popover>

        {/* Top Arrow */}
        <Button variant="outlined" onClick={handleClick2}>
          Top Arrow
        </Button>
        <Popover
          open={Boolean(anchorEl2)}
          anchorEl={anchorEl2}
          onClose={handleClose2}
          showArrow={true}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
        >
          <Box sx={{ p: 2, maxWidth: 200 }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              Top Arrow
            </Typography>
            <Typography variant="bodySmall">This popover has an arrow pointing down to the trigger</Typography>
          </Box>
        </Popover>

        {/* Right Arrow */}
        <Button variant="outlined" onClick={handleClick3}>
          Right Arrow
        </Button>
        <Popover
          open={Boolean(anchorEl3)}
          anchorEl={anchorEl3}
          onClose={handleClose3}
          showArrow={true}
          anchorOrigin={{
            vertical: 'center',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'center',
            horizontal: 'left',
          }}
        >
          <Box sx={{ p: 2, maxWidth: 200 }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              Right Arrow
            </Typography>
            <Typography variant="bodySmall">This popover has an arrow pointing left to the trigger</Typography>
          </Box>
        </Popover>

        {/* Left Arrow */}
        <Button variant="outlined" onClick={handleClick4}>
          Left Arrow
        </Button>
        <Popover
          open={Boolean(anchorEl4)}
          anchorEl={anchorEl4}
          onClose={handleClose4}
          showArrow={true}
          anchorOrigin={{
            vertical: 'center',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'center',
            horizontal: 'right',
          }}
        >
          <Box sx={{ p: 2, maxWidth: 200 }}>
            <Typography variant="titleSmall" sx={{ mb: 1 }}>
              Left Arrow
            </Typography>
            <Typography variant="bodySmall">This popover has an arrow pointing right to the trigger</Typography>
          </Box>
        </Popover>
      </Box>
    </Box>
  );
}
