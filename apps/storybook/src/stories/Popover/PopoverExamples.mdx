import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import * as PopoverStories from './Popover.stories';
import { Popover } from '@hxnova/react-components/Popover';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';

import PlacementExample from './Examples/PlacementExample';
import PlacementExampleSource from './Examples/PlacementExample.tsx?raw';

import PlacementTestExample from './Examples/PlacementTestExample';
import PlacementTestExampleSource from './Examples/PlacementTestExample.tsx?raw';

import CustomContentExample from './Examples/CustomContentExample';
import CustomContentExampleSource from './Examples/CustomContentExample.tsx?raw';

import BackdropExample from './Examples/BackdropExample';
import BackdropExampleSource from './Examples/BackdropExample.tsx?raw';

import ArrowExample from './Examples/ArrowExample';
import ArrowExampleSource from './Examples/ArrowExample.tsx?raw';

<Meta title="@hxnova/react-components/Popover/Examples" />

## Basic Usage

The basic Popover component provides a popup that appears when users click on a trigger element. It uses the standard MUI Popover API with `anchorEl`, `open`, and `onClose` props for state management.

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Popover Placement

The popover's position is controlled using `anchorOrigin` and `transformOrigin` props. These determine where the popover attaches to the trigger element and how it transforms when opening. Click on the buttons to see the popovers in different positions.

<div className="doc-story sb-story sb-unstyled">
  <PlacementExample />
</div>
<CodeExpand code={PlacementExampleSource} showBorderTop style={{marginTop: 16}}/>

## Placement Test (Debug)

This is a debug example to test each placement configuration individually with clear visual indicators.

<div className="doc-story sb-story sb-unstyled">
  <PlacementTestExample />
</div>
<CodeExpand code={PlacementTestExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom Content

You can provide any custom content as children of the Popover component. This allows you to create more complex popovers with custom layouts, interactive elements like buttons, and rich formatting.

<div className="doc-story sb-story sb-unstyled">
  <CustomContentExample />
</div>
<CodeExpand code={CustomContentExampleSource} showBorderTop style={{marginTop: 16}}/>

## Popover with Arrow

The popover can display an arrow pointing to the trigger element by setting the `showArrow` prop to `true`. The arrow automatically positions itself based on the popover's placement relative to its anchor element.

<div className="doc-story sb-story sb-unstyled">
  <ArrowExample />
</div>
<CodeExpand code={ArrowExampleSource} showBorderTop style={{marginTop: 16}}/>

## Backdrop

You can add a backdrop to the popover using the `slots` and `slotProps` API. This creates a semi-transparent overlay behind the popover that blocks interaction with the page.

<div className="doc-story sb-story sb-unstyled">
  <BackdropExample />
</div>
<CodeExpand code={BackdropExampleSource} showBorderTop style={{marginTop: 16}}/>
