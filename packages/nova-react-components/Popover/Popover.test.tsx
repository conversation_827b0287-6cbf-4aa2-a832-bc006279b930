import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Popover } from './Popover';

describe('Popover Arrow', () => {
  it('renders arrow when showArrow is true', () => {
    const anchorEl = document.createElement('div');
    render(
      <Popover open={true} anchorEl={anchorEl} onClose={() => {}} showArrow={true} data-testid="popover">
        <div>Test content</div>
      </Popover>,
    );

    // Check if arrow element is present
    const popover = screen.getByTestId('popover');
    const arrow = popover.querySelector('[class*="arrow"]');
    expect(arrow).toBeInTheDocument();
  });

  it('does not render arrow when showArrow is false', () => {
    const anchorEl = document.createElement('div');
    render(
      <Popover open={true} anchorEl={anchorEl} onClose={() => {}} showArrow={false} data-testid="popover">
        <div>Test content</div>
      </Popover>,
    );

    // Check if arrow element is not present
    const popover = screen.getByTestId('popover');
    const arrow = popover.querySelector('[class*="arrow"]');
    expect(arrow).not.toBeInTheDocument();
  });
});
