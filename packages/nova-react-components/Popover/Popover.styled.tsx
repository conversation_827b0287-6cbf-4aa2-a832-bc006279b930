import { styled } from '@pigment-css/react';
import { Modal } from '../Modal';
import { Paper } from '../Paper';

export const PopoverRoot = styled(Modal, {
  name: 'NovaPopover',
  slot: 'Root',
})({});

export const PopoverPaper = styled(Paper, {
  name: 'NovaPopover',
  slot: 'Paper',
})({
  position: 'absolute',
  overflow: 'visible', // Allow arrow to extend outside bounds in all directions
  // So we see the popover when it's empty.
  // It's most likely on issue on userland.
  minWidth: 16,
  minHeight: 16,
  maxWidth: 'calc(100% - 32px)',
  maxHeight: 'calc(100% - 32px)',
  // We disable the focus ring for mouse, touch and keyboard users.
  outline: 0,
  padding: '16px',
  borderRadius: '8px',
  margin: '8px',
});

export const PopoverArrow = styled('span', {
  name: 'NovaPopover',
  slot: 'Arrow',
})(({ theme }) => ({
  overflow: 'visible',
  position: 'absolute',
  width: '20px',
  height: '20px',
  boxSizing: 'border-box',
  zIndex: 1001, // Higher than paper to ensure visibility
  pointerEvents: 'none',
  // Default position will be overridden by inline styles

  // Create the arrow shape using clip-path for clean triangles without borders
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: '16px',
    height: '16px',
    backgroundColor: theme.vars.palette.surfaceContainer,
    clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)', // Upward pointing triangle
    transform: 'translate(-50%, -50%)',
    filter: `drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))`,
  },
}));
